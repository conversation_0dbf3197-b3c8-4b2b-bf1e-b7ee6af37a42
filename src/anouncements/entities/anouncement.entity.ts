import { Field, ObjectType } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';
import { MongooseSchema } from 'src/common/common.entity';
import { User, UserRoles } from 'src/users/entities/user.entity';

@ObjectType()
@Schema()
export class Anouncement extends MongooseSchema {
  @Field(() => [User], { nullable: true })
  @Prop({
    required: false,
    type: [{ type: mongoose.Schema.Types.ObjectId }],
  })
  users?: mongoose.Types.ObjectId[];

  @Field(() => [UserRoles], { nullable: true })
  @Prop({ required: false, type: [String], enum: UserRoles })
  userRoles?: UserRoles[];

  @Field(() => String)
  @Prop({ required: true })
  title: string;

  @Field(() => String)
  @Prop({ required: true })
  description: string;

  @Field(() => Date)
  @Prop({ required: true })
  date: Date;

  @Field(() => String, { nullable: true })
  @Prop({ required: false })
  document?: string;
}

export const AnouncementSchema = SchemaFactory.createForClass(Anouncement);
