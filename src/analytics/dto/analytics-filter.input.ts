import { Field, InputType } from '@nestjs/graphql';
import { UserRoles } from '../../users/entities/user.entity';

@InputType()
export class DateRangeInput {
  @Field()
  startDate: Date;

  @Field()
  endDate: Date;
}

@InputType()
export class AnalyticsFilterInput {
  @Field(() => DateRangeInput, { nullable: true })
  dateRange?: DateRangeInput;

  @Field(() => UserRoles, { nullable: true })
  userRole?: UserRoles;

  @Field(() => String, { nullable: true })
  locationId?: string;
}
