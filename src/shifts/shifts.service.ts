import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  addMinutes,
  differenceInMinutes,
  eachDayOfInterval,
  endOfYear,
  startOfToday,
} from 'date-fns';
import { AnyBulkWriteOperation, FilterQuery, Model } from 'mongoose';
import { extractDate } from 'src/utils';
import { CreateShiftInput } from './dto/create-shift.input';
import { UpdateShiftInput } from './dto/update-shift.input';
import { Shift } from './entities/shift.entity';

@Injectable()
export class ShiftsService {
  constructor(@InjectModel(Shift.name) private readonly shift: Model<Shift>) {}

  async create({
    locationId,
    userIds,
    startDateTime,
    endDateTime,
    overTime,
    isRecurring,
    recurringId,
  }: CreateShiftInput) {
    // bulk create shifts
    if (isRecurring) {
      const dates = eachDayOfInterval({
        start: extractDate(startDateTime),
        end: endOfYear(startOfToday()),
      });

      const diffInMins = differenceInMinutes(endDateTime, startDateTime);

      const bulkwriteOperation: AnyBulkWriteOperation<Shift>[] = dates.map(
        (date) => {
          const startTime = addMinutes(
            date,
            startDateTime.getHours() * 60 + startDateTime.getMinutes(),
          );
          const OT = addMinutes(
            date,
            overTime ? overTime.getHours() * 60 + overTime.getMinutes() : 0,
          );
          const endTime = addMinutes(startTime, diffInMins);

          return {
            insertOne: {
              document: new this.shift({
                startDateTime: startTime,
                endDateTime: endTime,
                location: locationId,
                overTime,
                OT,
                recurringId: recurringId || null,
                isRecurring,
                users: userIds,
              }),
            },
          };
        },
      );

      await this.shift.bulkWrite(bulkwriteOperation);
    } else {
      await this.shift.create({
        users: userIds,
        location: locationId,
        startDateTime,
        endDateTime,
        overTime,
        isRecurring,
        recurringId,
      });
    }
  }

  findAll(query: FilterQuery<Shift> = {}) {
    return this.shift.find(query);
  }

  findOne(query: FilterQuery<Shift>) {
    return this.shift.findOne(query);
  }

  update(id: string, updateShiftInput: UpdateShiftInput) {
    return this.shift.updateOne({ _id: id }, updateShiftInput);
  }

  async remove(id: string) {
    const shift = await this.shift.findOne({ _id: id });
    if (!shift) {
      throw new NotFoundException('Shift not found');
    }

    if (new Date() > shift.startDateTime) {
      throw new BadRequestException(
        'Cannot delete a shift that has already started',
      );
    }
    await this.shift.deleteOne({ _id: id });
  }

  async removeRecurringShifts(id: string, recurringId: string) {
    const shift = await this.shift.findOne({ _id: id, recurringId });
    if (!shift) {
      throw new NotFoundException('Shift not found');
    }
    await this.shift.deleteMany({
      recurringId,
      startDateTime: { $gte: new Date() },
    });
  }
}
