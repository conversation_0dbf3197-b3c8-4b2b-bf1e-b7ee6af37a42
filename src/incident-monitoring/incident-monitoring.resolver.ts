import {
  Resolver,
  Query,
  Mutation,
  Args,
  Context,
  ResolveField,
  Parent,
} from '@nestjs/graphql';
import { IncidentMonitoringService } from './incident-monitoring.service';
import { Incident } from './entities/incident-monitoring.entity';
import { CreateIncidentInput } from './dto/create-incident.input';
import { UpdateIncidentInput } from './dto/update-incident.input';
import { GqlContext } from 'src/app.module';
import { User } from 'src/users/entities/user.entity';
import { Location } from 'src/location/entities/location.entity';
import { CurrentUser } from 'src/auth/decorators/current-user.decorator';

@Resolver(() => Incident)
export class IncidentMonitoringResolver {
  constructor(
    private readonly incidentMonitoringService: IncidentMonitoringService,
  ) {}

  @ResolveField(() => User)
  reportedBy(@Parent() incident: Incident, @Context() context: GqlContext) {
    return context.loaders.usersLoader.load(incident.reportedBy);
  }

  @ResolveField(() => Location)
  location(@Parent() incident: Incident, @Context() context: GqlContext) {
    return context.loaders.locationLoader.load(incident.location);
  }

  @Mutation(() => Incident)
  createIncident(
    @Args('createIncidentInput') createIncidentInput: CreateIncidentInput,
    @CurrentUser() user: User,
  ) {
    return this.incidentMonitoringService.create(user.id, createIncidentInput);
  }

  @Query(() => [Incident], { name: 'incidents' })
  findAll() {
    return this.incidentMonitoringService.findAll();
  }

  @Query(() => Incident, { name: 'incident' })
  findOne(@Args('id', { type: () => String }) id: string) {
    return this.incidentMonitoringService.findOne(id);
  }

  @Mutation(() => Incident)
  updateIncident(
    @Args('id', { type: () => String }) id: string,
    @Args('updateIncidentInput') updateIncidentInput: UpdateIncidentInput,
  ) {
    return this.incidentMonitoringService.update(id, updateIncidentInput);
  }
}
