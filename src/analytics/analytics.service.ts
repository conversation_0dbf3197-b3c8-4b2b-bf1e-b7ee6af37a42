/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { startOfDay, endOfDay } from 'date-fns';
import { Attendance } from '../attendance/entities/attendance.entity';
import { Leave } from '../leaves/entities/leave.entity';
import { CheckpointAttendance } from '../checkpoint-attendance/entities/checkpoint-attendance.entity';
import { Incident } from '../incident-monitoring/entities/incident-monitoring.entity';
import { Inventory } from '../inventory/entities/inventory.entity';
import { InventoryRequest } from '../inventory/entities/inventory-request.entity';
import { AnalyticsFilterInput } from './dto/analytics-filter.input';
import {
  TimeSeriesData,
  GuardActivityStats,
  LeaveStats,
  InventoryStats,
} from './entities/analytics.entity';

@Injectable()
export class AnalyticsService {
  constructor(
    @InjectModel(Attendance.name) private attendanceModel: Model<Attendance>,
    @InjectModel(Leave.name) private leaveModel: Model<Leave>,
    @InjectModel(CheckpointAttendance.name)
    private checkpointAttendanceModel: Model<CheckpointAttendance>,
    @InjectModel(Incident.name) private incidentModel: Model<Incident>,
    @InjectModel(Inventory.name) private inventoryModel: Model<Inventory>,
    @InjectModel(InventoryRequest.name)
    private inventoryRequestModel: Model<InventoryRequest>,
  ) {}

  async getGuardAttendanceTrends(
    filter: AnalyticsFilterInput = {},
  ): Promise<TimeSeriesData[]> {
    const dateMatch = filter.dateRange
      ? {
          date: {
            $gte: startOfDay(filter.dateRange.startDate),
            $lte: endOfDay(filter.dateRange.endDate),
          },
        }
      : {};

    const roleMatch = filter.userRole ? { 'user.role': filter.userRole } : {};

    return this.attendanceModel.aggregate([
      { $match: { ...dateMatch, ...roleMatch } },
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m-%d', date: '$date' } },
          count: { $sum: 1 },
        },
      },
      {
        $project: {
          _id: 0,
          date: '$_id',
          count: 1,
        },
      },
      { $sort: { date: 1 } },
    ]);
  }

  async getGuardActivityStats(
    filter: AnalyticsFilterInput = {},
  ): Promise<GuardActivityStats> {
    const dateMatch = filter?.dateRange
      ? {
          createdAt: {
            $gte: filter.dateRange.startDate,
            $lte: filter.dateRange.endDate,
          },
        }
      : {};

    const [attendance, checkpoints, incidents] = await Promise.all([
      this.attendanceModel.aggregate([
        { $match: dateMatch },
        {
          $group: {
            _id: null,
            totalAttendance: { $sum: 1 },
            averageTimeSpent: { $avg: '$timeSpentInMinutes' },
          },
        },
      ]),
      this.checkpointAttendanceModel.countDocuments(dateMatch),
      this.incidentModel.countDocuments(dateMatch),
    ]);

    return {
      totalAttendance: attendance[0]?.totalAttendance || 0,
      averageTimeSpent: attendance[0]?.averageTimeSpent || 0,
      totalCheckpoints: checkpoints,
      totalIncidents: incidents,
    };
  }

  async getLeaveStats(filter: AnalyticsFilterInput = {}): Promise<LeaveStats> {
    const dateMatch = filter?.dateRange
      ? {
          startDateTime: {
            $gte: filter.dateRange.startDate,
            $lte: filter.dateRange.endDate,
          },
        }
      : {};

    const stats = await this.leaveModel.aggregate([
      { $match: dateMatch },
      {
        $group: {
          _id: '$leaveStatus',
          count: { $sum: 1 },
        },
      },
    ]);

    return {
      pending: stats.find((s) => s._id === 'pending')?.count || 0,
      approved: stats.find((s) => s._id === 'approved')?.count || 0,
      rejected: stats.find((s) => s._id === 'rejected')?.count || 0,
    };
  }

  async getInventoryStats(): Promise<InventoryStats[]> {
    const [stockStats, requestStats] = await Promise.all([
      // Get current stock statistics
      this.inventoryModel.aggregate([
        {
          $group: {
            _id: { $ifNull: ['$type', 'UNKNOWN'] },
            totalItems: { $sum: { $size: '$items' } },
          },
        },
      ]),
      // Get pending requests count
      this.inventoryRequestModel.aggregate([
        {
          $match: {
            status: 'pending',
          },
        },
        {
          $group: {
            _id: '$inventoryType',
            requestsPending: { $sum: 1 },
          },
        },
      ]),
    ]);

    // Merge the results
    const mergedStats = stockStats.map((stock) => {
      const requests = requestStats.find(
        // eslint-disable-next-line @typescript-eslint/no-unsafe-call
        (req) => req._id?.toString() === stock._id?.toString(),
      );

      return {
        type: stock._id,
        totalItems: stock.totalItems,
        requestsPending: requests?.requestsPending || 0,
      };
    });

    return mergedStats.filter((stat) => stat.type !== null);
  }
}
