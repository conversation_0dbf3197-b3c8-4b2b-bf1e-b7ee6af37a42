import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { CreateLeaveInput } from './dto/create-leave.input';
import { UpdateLeaveInput } from './dto/update-leave.input';
import { Leave } from './entities/leave.entity';
import { FilterQuery, Model } from 'mongoose';

@Injectable()
export class LeavesService {
  constructor(@InjectModel(Leave.name) private leave: Model<Leave>) {}

  create(createLeaveInput: CreateLeaveInput) {
    return this.leave.create(createLeaveInput);
  }

  findAll(filter: FilterQuery<Leave> = {}) {
    return this.leave.find(filter);
  }

  findOne(filter: FilterQuery<Leave>) {
    return this.leave.findOne(filter);
  }

  update(id: string, updateLeaveInput: UpdateLeaveInput) {
    return this.leave.findByIdAndUpdate(id, updateLeaveInput, { new: true });
  }
}
