import { Field, ObjectType, registerEnumType } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';
import { MongooseSchema } from 'src/common/common.entity';
import { Location } from 'src/location/entities/location.entity';
import { User } from 'src/users/entities/user.entity';

export enum PriorityLevel {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

registerEnumType(PriorityLevel, { name: 'PriorityLevel' });

export enum EvidenceType {
  IMAGE = 'IMAGE',
  VIDEO = 'VIDEO',
  DOCUMENT = 'DOCUMENT',
}

registerEnumType(EvidenceType, { name: 'EvidenceType' });

@ObjectType()
class Evidence {
  @Field(() => EvidenceType)
  @Prop({ required: true, enum: EvidenceType })
  type: EvidenceType;

  @Field()
  @Prop({ required: true })
  url: string;
}

@ObjectType()
@Schema({ collection: 'incident-monitoring' })
export class Incident extends MongooseSchema {
  @Field(() => Location)
  @Prop({ required: true, type: mongoose.Schema.Types.ObjectId })
  location: string;

  @Field()
  @Prop({ required: true })
  reportedAt: Date;

  @Field()
  @Prop({ required: true })
  description: string;

  @Field(() => PriorityLevel)
  @Prop({ required: true, type: String, enum: PriorityLevel })
  priorityLevel: PriorityLevel;

  @Field(() => [Evidence])
  @Prop({ type: [{ type: Object }] })
  evidence: Evidence[];

  @Field(() => User)
  @Prop({ required: true, type: mongoose.Schema.Types.ObjectId })
  reportedBy: string;
}

export const IncidentSchema = SchemaFactory.createForClass(Incident);
