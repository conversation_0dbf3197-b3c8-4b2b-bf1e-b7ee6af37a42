import { Field, InputType } from '@nestjs/graphql';
import { IsEnum } from 'class-validator';
import {
  PriorityLevel,
  EvidenceType,
} from '../entities/incident-monitoring.entity';

@InputType()
class EvidenceInput {
  @Field(() => EvidenceType)
  @IsEnum(EvidenceType)
  type: EvidenceType;

  @Field()
  url: string;
}

@InputType()
export class CreateIncidentInput {
  @Field()
  location: string;

  @Field()
  description: string;

  @Field(() => PriorityLevel)
  priorityLevel: PriorityLevel;

  @Field(() => [EvidenceInput])
  evidence: EvidenceInput[];
}
