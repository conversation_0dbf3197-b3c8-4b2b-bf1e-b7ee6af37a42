import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model } from 'mongoose';
import { Inventory } from './entities/inventory.entity';
import {
  InventoryRequest,
  RequestStatus,
} from './entities/inventory-request.entity';
import { CreateInventoryInput } from './dto/create-inventory.input';
import { CreateInventoryRequestInput } from './dto/create-inventory-request.input';
import { UpdateInventoryRequestInput } from './dto/update-inventory-request.input';

@Injectable()
export class InventoryService {
  constructor(
    @InjectModel(Inventory.name)
    private inventoryModel: Model<Inventory>,
    @InjectModel(InventoryRequest.name)
    private inventoryRequestModel: Model<InventoryRequest>,
  ) {}

  create(createInventoryInput: CreateInventoryInput) {
    return this.inventoryModel.create(createInventoryInput);
  }

  update(id: string, updateInventoryInput: CreateInventoryInput) {
    return this.inventoryModel.findByIdAndUpdate(id, updateInventoryInput, {
      new: true,
    });
  }

  findAll(query: FilterQuery<Inventory> = {}) {
    return this.inventoryModel.find(query);
  }

  findOne(query: FilterQuery<Inventory> = {}) {
    return this.inventoryModel.findOne(query);
  }

  async createRequest(createInput: CreateInventoryRequestInput) {
    // Fetch the inventory item first
    const inventory = await this.inventoryModel.findById(createInput.inventory);
    if (!inventory) {
      throw new NotFoundException(
        `Inventory with ID ${createInput.inventory} not found`,
      );
    }

    // Map the items with prices from inventory
    const items = await Promise.all(
      createInput.items.map((item) => {
        const inventoryItem = (
          (Array.isArray(inventory.items) ? inventory.items : []) as Array<{
            sku: string;
            costPrice: number;
            sellingPrice: number;
          }>
        )?.find((invItem) => invItem.sku === item.sku);

        if (!inventoryItem) {
          throw new NotFoundException(
            `Item with SKU ${item.sku} not found in inventory`,
          );
        }

        if (!inventoryItem) {
          throw new NotFoundException(
            `Item with SKU ${item.sku} not found in inventory`,
          );
        }

        return {
          ...item,
          costPrice: inventoryItem.costPrice,
          sellingPrice: inventoryItem.sellingPrice,
        };
      }),
    );

    // Create the request with updated items
    return this.inventoryRequestModel.create({
      ...createInput,
      items,
      inventoryType: inventory.type,
    });
  }

  async findAllRequests(
    filter: Partial<{
      inventoryType: string;
      status: RequestStatus;
      requestedBy: string;
    }> = {},
  ) {
    const query: Partial<{
      inventoryType: string;
      status: RequestStatus;
      requestedBy: string;
    }> = {};

    if (filter.inventoryType) {
      query.inventoryType = filter.inventoryType;
    }

    if (filter.status) {
      query.status = filter.status;
    }

    if (filter.requestedBy) {
      query.requestedBy = filter.requestedBy;
    }

    return this.inventoryRequestModel.find(query);
  }

  async findOneRequest(id: string) {
    const request = await this.inventoryRequestModel.findById(id);
    if (!request) {
      throw new NotFoundException(`Inventory request ${id} not found`);
    }
    return request;
  }

  async updateRequest(id: string, updateInput: UpdateInventoryRequestInput) {
    const request = await this.inventoryRequestModel.findByIdAndUpdate(
      id,
      {
        ...updateInput,
        ...(updateInput.status === RequestStatus.ACCEPTED && {
          acceptedAt: new Date(),
        }),
      },
      { new: true },
    );

    if (!request) {
      throw new NotFoundException(`Inventory request ${id} not found`);
    }
    return request;
  }
}
