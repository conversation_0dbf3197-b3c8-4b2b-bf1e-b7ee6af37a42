import {
  Injectable,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { differenceInMinutes, min } from 'date-fns';
import { FilterQuery, Model } from 'mongoose';
import { ShiftsService } from 'src/shifts/shifts.service';
import { ClockInInput, ClockOutInput } from './dto/clockin.input';
import { Attendance } from './entities/attendance.entity';
import { CreateAttendanceInput } from './dto/create-attendance.input';
import { Shift } from 'src/shifts/entities/shift.entity';
import { UpdateAttendanceInput } from './dto/update-attendance.input';
import { UsersService } from 'src/users/users.service';
@Injectable()
export class AttendanceService {
  constructor(
    @InjectModel(Attendance.name)
    private readonly attendance: Model<Attendance>,
    private readonly shiftService: ShiftsService,
    private readonly usersService: UsersService,
  ) {}

  private timeAndOverTimeSpent({
    shift,
    attendanceStartTime,
  }: {
    shift: Shift;
    attendanceStartTime: Date;
  }) {
    const currentDateTime = new Date();

    // Calculate total time spent from attendance start time until either the current time or shift end (whichever is earlier)
    const timeSpentInMinutes = differenceInMinutes(
      min([currentDateTime, shift.endDateTime]),
      attendanceStartTime,
    );

    // Calculate overtime spent only if overtime exists and user has worked past endDateTime
    let overTimeSpentInMinutes = 0;
    if (shift.overTime) {
      overTimeSpentInMinutes = differenceInMinutes(
        min([currentDateTime, shift.overTime]),
        shift.endDateTime,
      );
    }

    return {
      timeSpentInMinutes,
      overTimeSpentInMinutes,
    };
  }

  private async verifyUserFace(userId: string, base64Img: string) {
    const user = await this.usersService.findOne({ _id: userId });
    if (!user?.faceInformation?.faceId) {
      throw new UnauthorizedException(
        'Face data not registered for attendance',
      );
    }

    const faceMatches = await this.usersService.searchFace(base64Img);
    const isValidFace = faceMatches?.FaceMatches?.some(
      (face) => face.Face?.ExternalImageId === userId,
    );

    if (!isValidFace) {
      throw new UnauthorizedException('Face verification failed');
    }
  }

  private async validateClockIn(
    userId: string,
    shiftId: string,
  ): Promise<void> {
    // Get the shift details to check the time range
    const shift = await this.shiftService.findOne({ _id: shiftId });
    if (!shift) {
      throw new NotFoundException('Shift not found');
    }

    // Check if user is already clocked in for this shift within the shift's time range
    // We need to check for any attendance record that overlaps with the shift time range
    const existingAttendance = await this.attendance.findOne({
      user: userId,
      shift: shiftId,
      startTime: {
        $gte: shift.startDateTime,
        $lte: shift.endDateTime,
      },
      endTime: null, // No end time means still clocked in
    });

    if (existingAttendance) {
      throw new ConflictException('User is already clocked in for this shift');
    }
  }

  private async validateClockOut(attendance: Attendance): Promise<void> {
    // Check if user has already clocked out
    if (attendance.endTime) {
      throw new ConflictException(
        'User has already clocked out for this shift',
      );
    }
  }

  async clockIn(
    userId: string,
    { date, locationId, shiftId, base64Img }: ClockInInput,
  ) {
    await this.validateClockIn(userId, shiftId);
    await this.verifyUserFace(userId, base64Img);

    return this.attendance.create({
      location: locationId,
      shift: shiftId,
      user: userId,
      date,
      timeSpentInMinutes: 0,
      overTimeSpentInMinutes: 0,
      startTime: new Date(),
      endTime: null,
    });
  }

  async clockOut({ attendanceId, base64Img }: ClockOutInput) {
    const attendance = await this.attendance.findById(attendanceId);
    if (!attendance) throw new NotFoundException('Attendance not found');

    await this.validateClockOut(attendance);
    await this.verifyUserFace(attendance.user, base64Img);

    const shift = await this.shiftService.findOne({ _id: attendance.shift });
    if (!shift) throw new NotFoundException('Shift not found');

    const { timeSpentInMinutes, overTimeSpentInMinutes } =
      this.timeAndOverTimeSpent({
        shift,
        attendanceStartTime: attendance.startTime,
      });

    return this.attendance.findByIdAndUpdate(attendanceId, {
      endTime: new Date(),
      timeSpentInMinutes,
      overTimeSpentInMinutes,
    });
  }

  async createAttendance(attendance: CreateAttendanceInput) {
    const { date, locationId, shiftId, userId, startTime, endTime, overTime } =
      attendance;

    const shift = await this.shiftService.findOne({ _id: shiftId });
    if (!shift) throw new NotFoundException('Shift not found');

    const { timeSpentInMinutes, overTimeSpentInMinutes } =
      this.timeAndOverTimeSpent({
        shift,
        attendanceStartTime: startTime,
      });

    return this.attendance.create({
      location: locationId,
      shift: shiftId,
      user: userId,
      date,
      startTime,
      endTime,
      overTime,
      timeSpentInMinutes,
      overTimeSpentInMinutes,
    });
  }

  getAttendance(query: FilterQuery<Attendance> = {}) {
    return this.attendance.find(query);
  }

  findOne(filter: FilterQuery<Attendance>) {
    return this.attendance.findOne(filter);
  }

  async update(id: string, updateAttendanceInput: UpdateAttendanceInput) {
    const { id: _, ...updateData } = updateAttendanceInput;
    const { userId, date, startTime, endTime, overTime, locationId, shiftId } =
      updateData;
    return this.attendance.findByIdAndUpdate(
      id,
      {
        user: userId,
        date,
        startTime,
        endTime,
        overTime,
        location: locationId,
        shift: shiftId,
      },
      {
        new: true,
      },
    );
  }

  getAllAttendances() {
    return this.attendance.find();
  }
}
