import { Resolver, Mutation, Args, Query } from '@nestjs/graphql';
import { ClaimService } from './claim.service';
import { Claims } from './entities/claims.entity';
import { ClaimInput } from './dto/create-claim.input';
import { FindClaimsInput } from './dto/find-claims.input';
import { UpdateClaimInput } from './dto/update-claim.input';

@Resolver()
export class ClaimResolver {
  constructor(private readonly claimsService: ClaimService) {}

  @Mutation(() => Claims)
  createClaim(@Args('input') input: ClaimInput): Promise<Claims> {
    return this.claimsService.create(input);
  }

  @Query(() => [Claims], { name: 'claims' })
  findAll(
    @Args('filter', { type: () => FindClaimsInput, nullable: true })
    filter?: FindClaimsInput,
  ) {
    return this.claimsService.findAll(filter);
  }

  @Query(() => Claims, { name: 'claim' })
  findOne(@Args('id', { type: () => String }) id: string) {
    return this.claimsService.findOne({ _id: id });
  }

  @Mutation(() => Claims)
  updateClaim(
    @Args('id') id: string,
    @Args('updateClaimInput') updateClaimInput: UpdateClaimInput,
  ) {
    return this.claimsService.update(id, updateClaimInput);
  }
}
