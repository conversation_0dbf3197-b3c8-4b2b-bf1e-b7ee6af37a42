import { Field, ObjectType } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { MongooseSchema } from 'src/common/common.entity';
import { Checkpoint } from 'src/checkpoints/entities/checkpoint.entity';
import mongoose from 'mongoose';
import { Types } from 'mongoose';

@ObjectType()
@Schema()
export class Location extends MongooseSchema {
  @Field(() => String)
  @Prop({ required: true })
  name: string;

  @Field(() => String, { nullable: true })
  @Prop({ required: true })
  description?: string;

  @Field(() => String, { nullable: true })
  @Prop({ required: true })
  address?: string;

  @Field(() => String, { nullable: true })
  @Prop({ required: true })
  emergencyContact?: string;

  @Field(() => [Checkpoint], { nullable: true })
  @Prop({
    type: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Checkpoint' }],
    default: [],
  })
  checkpoints?: Types.ObjectId[];
}

export const LocationSchema = SchemaFactory.createForClass(Location);
