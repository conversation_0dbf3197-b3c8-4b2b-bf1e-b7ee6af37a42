import {
  Args,
  Context,
  Mutation,
  Parent,
  Query,
  ResolveField,
  Resolver,
} from '@nestjs/graphql';
import { GqlContext } from 'src/app.module';
import { User } from 'src/users/entities/user.entity';
import { AnouncementsService } from './anouncements.service';
import { CreateAnouncementInput } from './dto/create-anouncement.input';
import { UpdateAnouncementInput } from './dto/update-anouncement.input';
import { Anouncement } from './entities/anouncement.entity';

@Resolver(() => Anouncement)
export class AnouncementsResolver {
  constructor(private readonly anouncementsService: AnouncementsService) {}

  @ResolveField(() => [User], { name: 'users', nullable: true })
  async resolveUsers(
    @Parent() anouncement: Anouncement,
    @Context() context: GqlContext,
  ) {
    const userIds = anouncement.users?.map(String) ?? [];
    if (userIds.length === 0) return [];
    const users = await context.loaders.usersLoader.loadMany(userIds);
    return users.filter((u) => u != null);
  }

  @Mutation(() => Anouncement)
  createAnouncement(
    @Args('createAnouncementInput')
    createAnouncementInput: CreateAnouncementInput,
  ) {
    return this.anouncementsService.create(createAnouncementInput);
  }

  @Query(() => [Anouncement], { name: 'anouncements' })
  findAll() {
    return this.anouncementsService.findAll();
  }

  @Query(() => Anouncement, { name: 'anouncement' })
  findOne(@Args('id', { type: () => String }) id: string) {
    return this.anouncementsService.findOne(id);
  }

  @Mutation(() => Anouncement)
  updateAnouncement(
    @Args('id', { type: () => String }) id: string,
    @Args('updateAnouncementInput')
    updateAnouncementInput: UpdateAnouncementInput,
  ) {
    return this.anouncementsService.update(id, updateAnouncementInput);
  }

  @Mutation(() => Anouncement)
  removeAnouncement(@Args('id', { type: () => String }) id: string) {
    return this.anouncementsService.remove(id);
  }
}
