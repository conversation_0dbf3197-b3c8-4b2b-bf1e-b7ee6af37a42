import { Field, InputType } from '@nestjs/graphql';
import { IsDate, IsMongoId, IsNotEmpty, IsString } from 'class-validator';

@InputType()
export class ClockInInput {
  @Field()
  @IsMongoId()
  @IsNotEmpty()
  shiftId: string;

  @Field()
  @IsMongoId()
  @IsNotEmpty()
  locationId: string;

  @Field()
  @IsDate()
  date: Date;

  @Field(() => String)
  @IsString()
  base64Img: string;
}

@InputType()
export class ClockOutInput {
  @Field()
  @IsMongoId()
  @IsNotEmpty()
  attendanceId: string;

  @Field(() => String)
  @IsString()
  base64Img: string;
}
