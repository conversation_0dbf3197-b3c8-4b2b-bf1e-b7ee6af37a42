import { Resolver, Query, Args } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRoles } from '../users/entities/user.entity';
import { AnalyticsService } from './analytics.service';
import { AnalyticsFilterInput } from './dto/analytics-filter.input';
import {
  TimeSeriesData,
  GuardActivityStats,
  LeaveStats,
  InventoryStats,
} from './entities/analytics.entity';

@Resolver()
@UseGuards(RolesGuard)
@Roles(UserRoles.ADMIN, UserRoles.OPERATIONS_ADMIN, UserRoles.HR_ADMIN)
export class AnalyticsResolver {
  constructor(private readonly analyticsService: AnalyticsService) {}

  @Query(() => [TimeSeriesData])
  async guardAttendanceTrends(
    @Args('filter', { nullable: true }) filter?: AnalyticsFilterInput,
  ) {
    return this.analyticsService.getGuardAttendanceTrends(filter);
  }

  @Query(() => GuardActivityStats)
  async guardActivityStats(
    @Args('filter', { nullable: true }) filter?: AnalyticsFilterInput,
  ) {
    return this.analyticsService.getGuardActivityStats(filter);
  }

  @Query(() => LeaveStats)
  async leaveStats(
    @Args('filter', { nullable: true }) filter?: AnalyticsFilterInput,
  ) {
    return this.analyticsService.getLeaveStats(filter);
  }

  @Query(() => [InventoryStats])
  async inventoryStats() {
    return this.analyticsService.getInventoryStats();
  }
}
