import { InputType, Field, Int } from '@nestjs/graphql';
import { ClaimType } from '../entities/claims.entity';
import { ValidateIf } from 'class-validator';

@InputType()
export class ClaimInput {
  @Field(() => String)
  user: string;

  @Field(() => Number, { nullable: true })
  amount: number;

  @Field(() => String, { nullable: true })
  purpose?: string;

  @Field(() => ClaimType)
  claimType: ClaimType;

  // allowance claim
  @ValidateIf(
    (o: ClaimInput) =>
      o.claimType === ClaimType.ALLOWANCE || o.claimType === ClaimType.TRAVEL,
  )
  @Field(() => Date, { nullable: true })
  from: Date;

  @ValidateIf(
    (o: ClaimInput) =>
      o.claimType === ClaimType.ALLOWANCE || o.claimType === ClaimType.TRAVEL,
  )
  @Field(() => Date, { nullable: true })
  to: Date;

  @ValidateIf((o: ClaimInput) => o.claimType === ClaimType.ALLOWANCE)
  @Field(() => Int, { nullable: true })
  workingHours: number;

  @Field(() => [String], { nullable: true })
  receipts?: string[];

  // expensie claim
  @ValidateIf(
    (o: ClaimInput) =>
      o.claimType === ClaimType.EXPENSE || o.claimType === ClaimType.SITE,
  )
  @Field(() => [String], { nullable: true })
  items: string[];

  @ValidateIf((o: ClaimInput) => o.claimType === ClaimType.EXPENSE)
  @Field(() => Date, { nullable: true })
  date: Date;

  // travel claim
  @ValidateIf((o: ClaimInput) => o.claimType === ClaimType.TRAVEL)
  @Field(() => String, { nullable: true })
  client: string;

  @ValidateIf((o: ClaimInput) => o.claimType === ClaimType.TRAVEL)
  @Field(() => String, { nullable: true })
  toll: string;

  @ValidateIf((o: ClaimInput) => o.claimType === ClaimType.TRAVEL)
  @Field(() => Int, { nullable: true })
  distance: number;

  // site claim
  @ValidateIf((o: ClaimInput) => o.claimType === ClaimType.SITE)
  @Field(() => String, { nullable: true })
  site: string;
}
