import {
  Args,
  Context,
  Mutation,
  Parent,
  Query,
  ResolveField,
  Resolver,
} from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { AttendanceService } from './attendance.service';
import { ClockInInput, ClockOutInput } from './dto/clockin.input';
import { CurrentUser } from 'src/auth/decorators/current-user.decorator';
import { AttendanceInput } from './dto/attendance.input';
import { Shift } from 'src/shifts/entities/shift.entity';
import { GqlContext } from 'src/app.module';
import { Attendance } from './entities/attendance.entity';
import { User, UserRoles } from 'src/users/entities/user.entity';
import { Location } from 'src/location/entities/location.entity';
import { CreateAttendanceInput } from './dto/create-attendance.input';
import { RolesGuard } from 'src/auth/guards/roles.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { UpdateAttendanceInput } from './dto/update-attendance.input';
@Resolver(() => Attendance)
export class AttendanceResolver {
  constructor(private readonly attendanceService: AttendanceService) {}

  @ResolveField(() => Shift, { name: 'shift', nullable: true })
  getShift(@Parent() attendance: Attendance, @Context() context: GqlContext) {
    return context.loaders.shiftLoader.load(attendance.shift);
  }

  @ResolveField(() => User, { name: 'user', nullable: true })
  getUser(@Parent() attendance: Attendance, @Context() context: GqlContext) {
    return context.loaders.usersLoader.load(attendance.user);
  }

  @ResolveField(() => Location, { name: 'location', nullable: true })
  getLocation(
    @Parent() attendance: Attendance,
    @Context() context: GqlContext,
  ) {
    return context.loaders.locationLoader.load(attendance.location);
  }

  @Mutation(() => Attendance)
  clockIn(
    @Args('clockInInput') clockInInput: ClockInInput,
    @CurrentUser() user: User,
  ) {
    return this.attendanceService.clockIn(user.id, clockInInput);
  }

  @Mutation(() => Attendance)
  clockOut(@Args('clockOutInput') clockOutInput: ClockOutInput) {
    return this.attendanceService.clockOut(clockOutInput);
  }

  @Mutation(() => Attendance)
  @UseGuards(RolesGuard)
  @Roles(
    UserRoles.ADMIN,
    UserRoles.HR_ADMIN,
    UserRoles.OPERATIONS_ADMIN,
    UserRoles.OPERATIONS_MANAGER,
  )
  createAttendance(
    @Args('createAttendanceInput') createAttendanceInput: CreateAttendanceInput,
  ) {
    return this.attendanceService.createAttendance(createAttendanceInput);
  }

  @Query(() => Attendance)
  getAttendanceById(@Args('id', { type: () => String }) id: string) {
    return this.attendanceService.findOne({ _id: id });
  }

  @Query(() => [Attendance])
  @UseGuards(RolesGuard)
  @Roles(
    UserRoles.ADMIN,
    UserRoles.HR_ADMIN,
    UserRoles.OPERATIONS_ADMIN,
    UserRoles.OPERATIONS_MANAGER,
  )
  allAttendances(): Promise<Attendance[]> {
    return this.attendanceService.getAllAttendances();
  }

  @Mutation(() => Attendance)
  @UseGuards(RolesGuard)
  @Roles(
    UserRoles.ADMIN,
    UserRoles.HR_ADMIN,
    UserRoles.OPERATIONS_ADMIN,
    UserRoles.OPERATIONS_MANAGER,
  )
  updateAttendance(
    @Args('updateAttendanceInput') updateAttendanceInput: UpdateAttendanceInput,
  ) {
    return this.attendanceService.update(
      updateAttendanceInput.id,
      updateAttendanceInput,
    );
  }

  @Query(() => [Attendance])
  @UseGuards(RolesGuard)
  @Roles(
    UserRoles.ADMIN,
    UserRoles.HR_ADMIN,
    UserRoles.OPERATIONS_ADMIN,
    UserRoles.OPERATIONS_MANAGER,
  )
  attendance(
    @Args('attendanceInput')
    { shiftId }: AttendanceInput,
  ) {
    return this.attendanceService.getAttendance({
      shift: shiftId,
    });
  }
}
